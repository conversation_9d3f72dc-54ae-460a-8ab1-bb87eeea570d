import axios from 'axios';

// Get the current backend port from the window location or use a default
const getBackendUrl = () => {
  // Check if we have a stored backend URL in localStorage
  const storedBackendUrl = localStorage.getItem('backendApiUrl');
  if (storedBackendUrl) {
    console.log('Using stored API URL from localStorage');
    return storedBackendUrl;
  }

  // Use the environment variable from Vite
  const envUrl = import.meta.env.VITE_API_URL;
  if (envUrl) {
    console.log('Using API URL from environment variable');
    return envUrl;
  }

  // Default to relative path for API
  console.log('Using default API URL: /api');
  return '/api';
};

const API_URL = getBackendUrl();

// Log minimal API info for debugging
console.log('API initialized');

const api = axios.create({
  baseURL: API_URL,
  headers: {
    'Content-Type': 'application/json',
  },
  timeout: 5000, // 5 second timeout
});

// Add request interceptor for logging
api.interceptors.request.use(
  (config) => {
    console.log(`API Request: ${config.method.toUpperCase()} ${config.url}`);
    return config;
  },
  (error) => {
    console.error('API Request Error:', error);
    return Promise.reject(error);
  }
);

// Add response interceptor for logging
api.interceptors.response.use(
  (response) => {
    console.log(`API Response: ${response.status} ${response.config.method.toUpperCase()} ${response.config.url}`);
    return response;
  },
  (error) => {
    // Enhance error logging with more details
    if (error.response) {
      // The request was made and the server responded with a status code
      // that falls out of the range of 2xx
      console.error(`API Response Error: ${error.response.status} ${error.config?.method?.toUpperCase() || 'UNKNOWN'} ${error.config?.url || 'UNKNOWN'}`);
      console.error('Error data:', error.response.data);
    } else if (error.request) {
      // The request was made but no response was received
      console.error('API Response Error: No response received', error.config);
    } else {
      // Something happened in setting up the request that triggered an Error
      console.error('API Error:', error.message);
    }
    return Promise.reject(error);
  }
);

// Project endpoints
export const projectApi = {
  list: (baseDirectory) => {
    console.log('API: Making list request with base directory:', baseDirectory);
    return api.get(`/projects?base_directory=${encodeURIComponent(baseDirectory)}`);
  },
  create: (projectData) => {
    console.log('API: Making create request with data:', projectData);
    return api.post('/projects', projectData);
  },
  get: (projectPath) => {
    console.log('API: Making get request for path:', projectPath);
    return api.get(`/projects/${encodeURIComponent(projectPath)}`);
  },
  update: (projectPath, updateData) => {
    console.log('API: Making update request for path:', projectPath, 'with data:', updateData);
    return api.patch(`/projects/${encodeURIComponent(projectPath)}`, updateData);
  },
  check: (projectPath) => {
    console.log('API: Making check request for path:', projectPath);
    return api.post('/projects/check', { path: projectPath });
  },
  initialize: (projectData) => {
    console.log('API: Making initialize request with data:', projectData);
    return api.post('/projects/initialize', projectData);
  },
  load: (projectPath) => {
    console.log('API: Making load request for path:', projectPath);
    return api.get(`/projects/load?path=${encodeURIComponent(projectPath)}`);
  },
};

// Task endpoints
export const taskApi = {
  list: (projectPath, status = null) => {
    let url = `/tasks/${encodeURIComponent(projectPath)}`;
    if (status) {
      url += `?status=${status}`;
    }
    return api.get(url);
  },
  create: (projectPath, taskData) => api.post(`/tasks/${encodeURIComponent(projectPath)}`, taskData),
  update: (projectPath, taskId, updateData) => api.patch(`/tasks/${encodeURIComponent(projectPath)}/${taskId}`, updateData),
  delete: (projectPath, taskId) => api.delete(`/tasks/${encodeURIComponent(projectPath)}/${taskId}`),
};

// Graph endpoints
export const graphApi = {
  getData: (projectPath) => api.get(`/graph/${encodeURIComponent(projectPath)}/data`),
  getImageUrl: (projectPath, format = 'png') => `${API_URL}/graph/${encodeURIComponent(projectPath)}/image?format=${format}`,
};

// LLM endpoints
export const llmApi = {
  listProviders: () => api.get('/llm/providers'),
  sendPrompt: (promptData) => api.post('/llm/prompt', promptData),
};

// MVCD endpoints
export const mvcdApi = {
  getStatus: (projectPath) => {
    console.log('API: Getting MVCD status for project:', projectPath);
    return api.get(`/mvcd/status?project_path=${encodeURIComponent(projectPath)}`);
  },
  getData: (projectPath) => {
    console.log('API: Getting MVCD data for project:', projectPath);
    return api.get(`/mvcd/data?project_path=${encodeURIComponent(projectPath)}`);
  },
  generate: (projectPath) => {
    console.log('API: Generating MVCD for project:', projectPath);
    return api.post('/mvcd/generate', { project_path: projectPath });
  },
  enrich: (projectPath, options = {}) => {
    console.log('API: Enriching MVCD for project:', projectPath, 'with options:', options);
    return api.post('/mvcd/enrich', {
      project_path: projectPath,
      coding_agent_type: options.coding_agent_type || 'augment',
      headless: options.headless || false,
      timeout: options.timeout || 300
    });
  },
  analyze: (projectPath) => {
    console.log('API: Analyzing MVCD for project:', projectPath);
    return api.post('/mvcd/analyze', { project_path: projectPath });
  },
  getTaskStatus: (taskId) => {
    console.log('API: Getting task status for task:', taskId);
    return api.get(`/mvcd/task/${taskId}`);
  },
  getIgnore: (projectPath) => {
    console.log('API: Getting MVCD ignore data for project:', projectPath);
    return api.get(`/mvcd/ignore?project_path=${encodeURIComponent(projectPath)}`);
  },
  saveIgnore: (projectPath, content) => {
    console.log('API: Saving MVCD ignore data for project:', projectPath);
    return api.post('/mvcd/ignore', { project_path: projectPath, content: content });
  },
  getImprovements: (projectPath) => {
    console.log('API: Getting improvements for project:', projectPath);
    return api.get(`/mvcd/improvements?project_path=${encodeURIComponent(projectPath)}`);
  },
  updateImprovement: (projectPath, improvementId, priority, doneTimestamp) => {
    console.log('API: Updating improvement for project:', projectPath, 'improvement:', improvementId);
    return api.post('/mvcd/improvements', {
      project_path: projectPath,
      improvement_id: improvementId,
      priority: priority,
      done_timestamp: doneTimestamp
    });
  }
};

// Export both the API instance and the URL
export { API_URL };
export default api;
